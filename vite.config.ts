import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: true, // Listen on all addresses
    port: 5173,
    strictPort: true, // Ensure Vite uses this port or fails if it's in use
    open: true, // Open browser automatically
    hmr: {
      // Explicitly configure HMR WebSocket connection
      protocol: 'ws',
      host: 'localhost',
      port: 5173
    },
    // Disable service worker in development
    middlewareMode: false,
    fs: {
      // Allow serving files from one level up to the project root
      allow: ['..']
    }
  },
  plugins: [
    react(),
    // mode === 'development' && componentTagger(), // Temporarily commented out
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Simplified build configuration for better Vercel compatibility
  build: {
    // Generate source maps for production
    sourcemap: mode === 'production',
    // Basic configuration for CSS
    cssCodeSplit: true,
    // Optimize for production
    minify: mode === 'production' ? 'esbuild' : false,
    // Rollup options for better error handling
    rollupOptions: {
      onwarn(warning, warn) {
        // Skip certain warnings in development
        if (mode === 'development' && warning.code === 'UNUSED_EXTERNAL_IMPORT') {
          return;
        }
        warn(warning);
      }
    }
  },
  // Optimize preview server
  preview: {
    port: 8080,
    // Enable HTTP compression for better performance
    compress: true,
  },
}));
