import React, { useEffect } from 'react';

/**
 * ResourceHints component to improve loading performance by providing resource hints to the browser
 * This component should be included in the App component
 */
const ResourceHints: React.FC = () => {
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    try {
      // Add preconnect for external domains
      addPreconnect([
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://cdn.vercel-insights.com'
      ]);

      // Add DNS prefetch for less critical domains
      addDnsPrefetch([
        'https://vercel.com'
      ]);

      // Preload critical fonts
      preloadFonts([
        // Add your critical fonts here if any
      ]);

      // Note: CSS preloading removed to prevent unused preload warnings
      // Vite handles CSS loading automatically and efficiently
    } catch (error) {
      console.warn('ResourceHints: Error setting up resource hints:', error);
    }
  }, []);

  return null; // This component doesn't render anything
};

function addPreconnect(urls: string[]) {
  urls.forEach(url => {
    try {
      if (!document.querySelector(`link[rel="preconnect"][href="${url}"]`)) {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = url;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn(`Failed to add preconnect for ${url}:`, error);
    }
  });
}

function addDnsPrefetch(urls: string[]) {
  urls.forEach(url => {
    try {
      if (!document.querySelector(`link[rel="dns-prefetch"][href="${url}"]`)) {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = url;
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn(`Failed to add DNS prefetch for ${url}:`, error);
    }
  });
}

function preloadFonts(fontUrls: string[]) {
  fontUrls.forEach(url => {
    try {
      if (!document.querySelector(`link[rel="preload"][href="${url}"]`)) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = url;
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn(`Failed to preload font ${url}:`, error);
    }
  });
}



export default ResourceHints;
