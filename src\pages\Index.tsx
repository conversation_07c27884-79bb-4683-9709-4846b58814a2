import React, { useEffect, Suspense, lazy } from 'react';
import { LanguageProvider } from '@/context/LanguageContext';
import { useScrollReveal } from '@/hooks/useScrollReveal';
import Navbar from '@/components/Navbar';
import HeroSection from '@/components/HeroSection';
import Footer from '@/components/Footer';

// Lazy load sections for better performance
const BenefitsSection = lazy(() => import('@/components/BenefitsSection'));
const GallerySection = lazy(() => import('@/components/GallerySection'));
const PortfolioSection = lazy(() => import('@/components/PortfolioSection'));
const PricingSection = lazy(() => import('@/components/PricingSection'));
const TestimonialsSection = lazy(() => import('@/components/TestimonialsSection'));
const ContactSection = lazy(() => import('@/components/ContactSection'));

// Loading fallback component for sections
const SectionLoader = () => (
  <div className="flex items-center justify-center py-20">
    <div className="w-8 h-8 border-2 border-neon-cyan/20 border-t-neon-cyan rounded-full animate-spin"></div>
  </div>
);

const Index = () => {
  useScrollReveal();

  // Update the page title and meta tags
  useEffect(() => {
    // Set basic meta data
    document.title = 'Econic Media | Product Photography & Branding Design Agency';

    // Add meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.setAttribute('name', 'description');
      document.head.appendChild(metaDescription);
    }
    metaDescription.setAttribute('content', 'Transform your products with studio-quality photography without shipping and professional branding design. Boost conversions with our e-commerce visual solutions.');

    // Add canonical link
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', 'https://econicmedia.pro');

    // Add Open Graph meta tags
    const ogTags = [
      { property: 'og:title', content: 'Econic Media | Product Photography & Branding Design Agency' },
      { property: 'og:description', content: 'Transform your products with studio-quality photography without shipping and professional branding design. Boost conversions with our e-commerce visual solutions.' },
      { property: 'og:type', content: 'website' },
      { property: 'og:url', content: 'https://econicmedia.pro' },
      { property: 'og:image', content: 'https://econicmedia.pro/lovable-uploads/f71aca91-6b27-4100-a40e-883ce27ec690.png' }
    ];

    ogTags.forEach(tag => {
      let ogTag = document.querySelector(`meta[property="${tag.property}"]`);
      if (!ogTag) {
        ogTag = document.createElement('meta');
        ogTag.setAttribute('property', tag.property);
        document.head.appendChild(ogTag);
      }
      ogTag.setAttribute('content', tag.content);
    });

    // Add Twitter Card meta tags
    const twitterTags = [
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: 'Econic Media | Product Photography & Branding Design Agency' },
      { name: 'twitter:description', content: 'Transform your products with studio-quality photography without shipping and professional branding design. Boost conversions with our e-commerce visual solutions.' },
      { name: 'twitter:image', content: 'https://econicmedia.pro/lovable-uploads/f71aca91-6b27-4100-a40e-883ce27ec690.png' }
    ];

    twitterTags.forEach(tag => {
      let twitterTag = document.querySelector(`meta[name="${tag.name}"]`);
      if (!twitterTag) {
        twitterTag = document.createElement('meta');
        twitterTag.setAttribute('name', tag.name);
        document.head.appendChild(twitterTag);
      }
      twitterTag.setAttribute('content', tag.content);
    });

  }, []);

  return (
    <LanguageProvider>
      <div className="min-h-screen bg-background text-foreground">
        <Navbar />

        <HeroSection />

        <Suspense fallback={<SectionLoader />}>
          <BenefitsSection />
        </Suspense>
        
        <Suspense fallback={<SectionLoader />}>
          <GallerySection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <PortfolioSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <PricingSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <TestimonialsSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <ContactSection />
        </Suspense>

        <Footer />
      </div>
    </LanguageProvider>
  );
};

export default Index;
