// Service Worker for Econic Media Website
// Improves performance through caching and offline capabilities
// Enhanced with better error handling and frame management

const CACHE_NAME = 'econic-media-cache-v4';

// Check if we're in development mode
const isDevelopment = self.location.hostname === 'localhost' || self.location.hostname === '127.0.0.1';

// Enhanced error handling function
const handleServiceWorkerError = (error, context = 'general') => {
  const errorMessage = error?.message || error?.toString() || '';

  // Filter out browser extension and frame-related errors
  if (errorMessage.includes('Frame with ID') ||
      errorMessage.includes('No tab with id') ||
      errorMessage.includes('message port closed') ||
      errorMessage.includes('Extension context') ||
      errorMessage.includes('background.js') ||
      errorMessage.includes('contentscript.bundle.js')) {
    return; // Silently ignore extension-related errors
  }

  // Only log actual service worker errors in production
  if (!isDevelopment) {
    console.error(`Service Worker ${context} error:`, error);
  }
};

// Skip service worker functionality in development to avoid console errors
if (isDevelopment) {
  console.log('Service Worker: Development mode detected, minimal functionality enabled');
}
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/index.css',
  '/Product Pictures/1 (1).png',
  '/Product Pictures/1 (2).png',
  '/Product Pictures/1 (3).png',
  '/Product Pictures/1 (4).png',
  '/Product Pictures/1 (5).png',
  '/Product Pictures/1 (6).png',
  '/Product Pictures/1 (7).png',
  '/Product Pictures/1 (8).png',
  // Also cache URL-encoded versions to handle browser encoding
  '/Product%20Pictures/1%20(1).png',
  '/Product%20Pictures/1%20(2).png',
  '/Product%20Pictures/1%20(3).png',
  '/Product%20Pictures/1%20(4).png',
  '/Product%20Pictures/1%20(5).png',
  '/Product%20Pictures/1%20(6).png',
  '/Product%20Pictures/1%20(7).png',
  '/Product%20Pictures/1%20(8).png',
  // Add remaining product images (9-24)
  '/Product Pictures/1 (9).png',
  '/Product Pictures/1 (10).png',
  '/Product Pictures/1 (11).png',
  '/Product Pictures/1 (12).png',
  '/Product Pictures/1 (13).png',
  '/Product Pictures/1 (14).png',
  '/Product Pictures/1 (15).png',
  '/Product Pictures/1 (16).png',
  '/Product Pictures/1 (17).png',
  '/Product Pictures/1 (18).png',
  '/Product Pictures/1 (19).png',
  '/Product Pictures/1 (20).png',
  '/Product Pictures/1 (21).png',
  '/Product Pictures/1 (22).png',
  '/Product Pictures/1 (23).png',
  '/Product Pictures/1 (24).png',
  // URL-encoded versions for remaining images
  '/Product%20Pictures/1%20(9).png',
  '/Product%20Pictures/1%20(10).png',
  '/Product%20Pictures/1%20(11).png',
  '/Product%20Pictures/1%20(12).png',
  '/Product%20Pictures/1%20(13).png',
  '/Product%20Pictures/1%20(14).png',
  '/Product%20Pictures/1%20(15).png',
  '/Product%20Pictures/1%20(16).png',
  '/Product%20Pictures/1%20(17).png',
  '/Product%20Pictures/1%20(18).png',
  '/Product%20Pictures/1%20(19).png',
  '/Product%20Pictures/1%20(20).png',
  '/Product%20Pictures/1%20(21).png',
  '/Product%20Pictures/1%20(22).png',
  '/Product%20Pictures/1%20(23).png',
  '/Product%20Pictures/1%20(24).png',
  '/lovable-uploads/70026725-9e78-48a3-8dd2-c8b817a3fba4.png', // Hero image
  // Portfolio website images
  '/Websites/rein-glanz-service.png',
  '/Websites/benfresh.png',
  '/Websites/callsaver.png',
  '/Websites/aurora-dental-clinic-min.png',
  '/Websites/securitas-security-min.png',
  '/Websites/hogan-lovells-germany-min.png',
  '/Websites/cleanwhale-berlin-min.png',
  '/Websites/superlist-productivity-min.png',
  '/Websites/linear-dev-tools-min.png',
  '/Websites/pitch-presentation-min.png',
];

// Installation event - cache core assets
self.addEventListener('install', (event) => {
  // In development, skip caching to avoid errors
  if (isDevelopment) {
    console.log('Service Worker: Install event - skipping cache in development');
    self.skipWaiting();
    return;
  }

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
      .catch((error) => {
        handleServiceWorkerError(error, 'installation');
      })
  );
});

// Activation event - clean up old caches
self.addEventListener('activate', (event) => {
  // In development, skip cache cleanup
  if (isDevelopment) {
    console.log('Service Worker: Activate event - skipping cache cleanup in development');
    self.clients.claim();
    return;
  }

  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((name) => {
          if (name !== CACHE_NAME) {
            if (!isDevelopment) {
              console.log('Deleting old cache:', name);
            }
            return caches.delete(name);
          }
        })
      );
    }).then(() => {
      if (!isDevelopment) {
        console.log('Service worker activated and old caches cleaned');
      }
      return self.clients.claim();
    }).catch((error) => {
      handleServiceWorkerError(error, 'activation');
    })
  );
});

// Network first with cache fallback strategy for images
const imageStrategy = async (request) => {
  try {
    // Try network first
    const networkResponse = await fetch(request);

    // Cache the response if it's valid
    if (networkResponse && networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // If network fails, try cache with both encoded and decoded URLs
    let cachedResponse = await caches.match(request);

    if (!cachedResponse) {
      // Try with URL decoded version
      const decodedUrl = decodeURIComponent(request.url);
      const decodedRequest = new Request(decodedUrl, request);
      cachedResponse = await caches.match(decodedRequest);
    }

    if (!cachedResponse) {
      // Try with URL encoded version
      const encodedUrl = encodeURI(request.url);
      const encodedRequest = new Request(encodedUrl, request);
      cachedResponse = await caches.match(encodedRequest);
    }

    return cachedResponse || Promise.reject('Not found in cache');
  }
};

// Cache first with network fallback for static assets
const cacheFirstStrategy = async (request) => {
  // Try the cache first
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    // If not found in cache, fetch from network and cache it
    const networkResponse = await fetch(request);

    if (networkResponse && networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // If both cache and network fail
    return Promise.reject('Resource not available');
  }
};

// Fetch event - intercept requests and apply caching strategies
self.addEventListener('fetch', (event) => {
  // In development, skip fetch interception to avoid errors
  if (isDevelopment) {
    return;
  }

  // Skip cross-origin requests to avoid CORS issues with Vercel
  const url = new URL(event.request.url);
  if (url.origin !== self.location.origin) {
    return;
  }

  // Don't cache API calls
  if (url.pathname.startsWith('/api/')) {
    return;
  }

  try {
    // Apply image strategy for image files
    if (event.request.destination === 'image' || url.pathname.match(/\.(png|jpg|jpeg|gif|webp|svg|ico)$/i)) {
      event.respondWith(imageStrategy(event.request));
      return;
    }

    // Apply cache-first strategy for static assets
    if (event.request.destination === 'style' ||
        event.request.destination === 'script' ||
        event.request.destination === 'font' ||
        url.pathname.match(/\.(css|js|woff2?)$/i)) {
      event.respondWith(cacheFirstStrategy(event.request));
      return;
    }

    // Network-first for HTML documents
    if (event.request.mode === 'navigate' || event.request.destination === 'document') {
      event.respondWith(
        fetch(event.request).catch(() => {
          return caches.match(event.request);
        })
      );
      return;
    }
  } catch (error) {
    handleServiceWorkerError(error, 'fetch');
  }
});

// Add global error handler for unhandled service worker errors
self.addEventListener('error', (event) => {
  handleServiceWorkerError(event.error, 'global');
});

// Add unhandled promise rejection handler
self.addEventListener('unhandledrejection', (event) => {
  handleServiceWorkerError(event.reason, 'promise rejection');
  event.preventDefault(); // Prevent the error from appearing in console
});
