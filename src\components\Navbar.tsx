import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { X, Menu, Home, Briefcase, Image, Globe, DollarSign, Star, PhoneCall } from 'lucide-react';

const Navbar: React.FC = () => {
  const { t } = useLanguage();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState<string>('home');
  const navRef = useRef<HTMLElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const navLinks = useMemo(() => [
    { href: '#home', label: t('nav.home'), section: 'home', icon: Home },
    { href: '#expertise', label: t('nav.services'), section: 'expertise', icon: Briefcase },
    { href: '#gallery', label: t('nav.gallery'), section: 'gallery', icon: Image },
    { href: '#websites', label: 'Websites', section: 'websites', icon: Globe },
    { href: '#pricing', label: t('nav.pricing'), section: 'pricing', icon: DollarSign },
    { href: '#testimonials', label: t('nav.testimonials'), section: 'testimonials', icon: Star },
    { href: '#contact', label: t('nav.contact'), section: 'contact', icon: PhoneCall },
  ], [t]);

  // Handle scroll events for navbar styling and active section detection
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
      
      try {
        // Determine active section - safely
        const scrollPosition = window.scrollY + window.innerHeight / 3;
        let foundActive = false;
        
        // Check each section from bottom to top of page (most reliable detection method)
        for (let i = navLinks.length - 1; i >= 0; i--) {
          const section = document.getElementById(navLinks[i].section);
          if (section && section.offsetTop <= scrollPosition) {
            setActiveSection(navLinks[i].section);
            foundActive = true;
            break;
          }
        }
        
        // Default to home if nothing is active
        if (!foundActive && navLinks.length > 0) {
          setActiveSection('home');
        }
      } catch (err) {
        // Fail silently but keep navbar functional
        console.warn('Section detection error:', err);
      }
    };

    window.addEventListener('scroll', handleScroll);
    
    // Wait for DOM to be fully ready before initial detection
    const timer = setTimeout(() => {
      handleScroll();
    }, 100);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timer);
    };
  }, [navLinks]);

  // Handle click outside to close mobile menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node) && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  // Add ambient light effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const nav = navRef.current;
      if (nav) {
        const rect = nav.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        if (x > 0 && x < rect.width && y > 0 && y < rect.height + 100) {
          const percentX = x / rect.width;
          const percentY = y / rect.height;
          
          nav.style.background = `radial-gradient(circle at ${percentX * 100}% ${percentY * 100}%, rgba(255,255,255,0.15), rgba(255,255,255,0.03))`;
          nav.style.borderColor = `rgba(255,255,255,${0.1 + percentY * 0.1})`;
        }
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <>
      {/* Ambient glow effects - maintain existing background effects */}
      <div className="fixed top-0 left-1/4 -translate-x-1/2 w-40 h-40 rounded-full bg-purple-500/20 blur-3xl pointer-events-none"></div>
      <div className="fixed top-0 right-1/4 translate-x-1/2 w-40 h-40 rounded-full bg-blue-500/20 blur-3xl pointer-events-none"></div>
      
      {/* Improved Navbar with proper container constraints and layout */}
      {/* Enhanced Mobile Menu Button - Fixed Position with improved visibility */}
      <button
        type="button"
        className="block md:hidden fixed right-4 top-4 z-[9999] flex items-center justify-center w-12 h-12 rounded-full
        bg-gradient-to-br from-neon-cyan/90 to-neon-purple/90 backdrop-blur-md
        shadow-[0_0_20px_rgba(0,229,229,0.4),0_0_20px_rgba(179,102,255,0.4)]
        border-2 border-white/30 touch-manipulation active:scale-95
        transition-all duration-200 ease-in-out hover:scale-105
        safe-area-inset-top safe-area-inset-right"
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        aria-label="Toggle navigation menu"
        aria-expanded={mobileMenuOpen ? "true" : "false"}
      >
        {mobileMenuOpen ? (
          <X className="w-5 h-5 text-white drop-shadow-sm" />
        ) : (
          <Menu className="w-5 h-5 text-white drop-shadow-sm" />
        )}
      </button>
      
      {/* Desktop Navigation - Hidden on Mobile */}
      <nav 
        ref={navRef} 
        className="fixed top-2 sm:top-4 z-40 transition-all duration-300 left-0 right-0 hidden md:flex justify-center w-full"
      >
        {/* Container with optimized width for better UI */}
        <div 
          className={`w-auto max-w-[95%] sm:max-w-[90%] md:max-w-xl lg:max-w-2xl rounded-full 
            ${scrolled ? 'bg-white/10' : 'bg-white/5'} 
            backdrop-blur-2xl border border-white/20 
            shadow-[0_0_30px_rgba(139,92,246,0.2),0_0_30px_rgba(14,165,233,0.2)] 
            overflow-hidden`}
        >
          <div className="w-full px-1 sm:px-2 py-2.5 flex items-center justify-between relative z-10">
            {/* Left side - Empty placeholder */}
            <div className="flex items-center">
              {/* Logo removed */}
            </div>

            {/* Desktop Menu - Center aligned for larger screens */}
            <div className="flex items-center justify-center flex-1 px-1 sm:px-2">
              <ul className="flex items-center justify-center gap-3 sm:gap-4 lg:gap-5">
                {navLinks.map((link) => (
                  <li key={link.href}>
                    <a
                      href={link.href}
                      className={`text-sm ${activeSection === link.section ? 'text-neon-cyan after:scale-x-100' : 'text-foreground/80'} hover:text-neon-cyan transition-colors relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-neon-cyan after:scale-x-0 after:origin-right after:transition-transform hover:after:scale-x-100 hover:after:origin-left`}
                      onClick={(e) => {
                        e.preventDefault();
                        try {
                          const element = document.getElementById(link.section);
                          if (element) {
                            window.scrollTo({
                              top: element.offsetTop,
                              behavior: 'smooth'
                            });
                          }
                        } catch (err) {
                          console.warn('Navigation error:', err);
                          // Fallback to regular link behavior
                          window.location.hash = link.href;
                        }
                      }}
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Empty - Mobile Menu Button is now outside this container */}
          </div>
        </div>
      </nav>

      {/* Enhanced Mobile Menu with slide-down animation and improved z-index */}
      <div
        id="mobile-menu"
        className={`fixed inset-0 md:hidden bg-gradient-to-b from-black/90 via-black/85 to-black/90 backdrop-blur-xl z-[9998]
          transition-all duration-500 ease-out overflow-hidden
          ${mobileMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'}`}
      >
        <div
          ref={mobileMenuRef}
          className={`fixed top-0 left-1/2 -translate-x-1/2 w-auto min-w-[280px] max-w-md
          bg-gradient-to-br from-[#0e1b2e] via-[#12213a] to-[#0e1b2e] backdrop-blur-xl z-[9999]
          border border-[#264f82]/40 rounded-b-2xl overflow-hidden
          shadow-[0_8px_30px_rgba(36,99,175,0.3),0_8px_30px_rgba(94,53,177,0.3)]
          transition-all duration-400 ease-in-out transform origin-top
          ${mobileMenuOpen ? 'scale-y-100 opacity-100' : 'scale-y-0 opacity-0'}`}
        >
          <div className="flex flex-col pt-5 pb-6 overflow-y-auto relative">
            {/* Colorful gradient background */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#264f82]/10 via-transparent to-[#5e35b1]/10 pointer-events-none"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-[#0e1b2e]/50 pointer-events-none"></div>
          
            {/* Navigation links with improved styling aligned with design system */}
            <div className="flex flex-col gap-2 px-4 relative">
              {navLinks.map((link) => (
                <div key={link.href}>
                  <a
                    href={link.href}
                    className={`relative font-outfit text-base px-5 py-3.5 rounded-lg min-h-[52px] flex items-center gap-3 
                      ${activeSection === link.section 
                        ? 'text-white font-medium bg-gradient-to-r from-[#264f82] to-[#5e35b1] pl-4 shadow-[0_0_15px_rgba(36,99,175,0.3)]' 
                        : 'text-[#a0b9d6] hover:text-white bg-[#12213a]/40 hover:bg-[#12213a]/70'} 
                      hover:shadow-md active:bg-[#12213a]/90 
                      transition-all duration-300 touch-manipulation`}
                    onClick={(e) => {
                      e.preventDefault();
                      setMobileMenuOpen(false);
                      try {
                        // Small delay to allow menu to close first
                        setTimeout(() => {
                          const element = document.getElementById(link.section);
                          if (element) {
                            window.scrollTo({
                              top: element.offsetTop,
                              behavior: 'smooth'
                            });
                          }
                        }, 300); // Delay for better animation completion
                      } catch (err) {
                        console.warn('Mobile navigation error:', err);
                        // Fallback to regular link behavior
                        window.location.hash = link.href;
                      }
                    }}
                  >
                    <span className="w-5 h-5 flex items-center justify-center text-[#5e9dff]">
                      {React.createElement(link.icon, { size: 18 })}
                    </span>
                    {link.label}
                  </a>
                </div>
              ))}
            </div>
            
            {/* Visual design element consistent with site aesthetic */}
            <div className="h-px bg-gradient-to-r from-[#264f82]/40 via-[#5e35b1]/40 to-transparent mx-4 my-4"></div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Navbar;
