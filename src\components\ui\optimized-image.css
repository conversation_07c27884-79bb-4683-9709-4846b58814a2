.optimized-image-container {
  position: relative;
  overflow: hidden;
  width: var(--width, 100%);
  height: var(--height, auto);
}

.optimized-image-placeholder {
  position: absolute;
  inset: 0;
  background-color: rgba(200, 200, 200, 0.2);
  animation: pulse 1.5s infinite ease-in-out;
}

.optimized-image {
  transition: opacity 300ms;
}

/* Object-fit utility classes */
.object-fit-cover {
  object-fit: cover;
}

.object-fit-contain {
  object-fit: contain;
}

.object-fit-fill {
  object-fit: fill;
}

.object-fit-none {
  object-fit: none;
}

.object-fit-scale-down {
  object-fit: scale-down;
}

.optimized-image--loaded {
  opacity: 1;
}

.optimized-image--loading {
  opacity: 0;
}

.optimized-image--empty-placeholder {
  width: 100%;
  height: 100%;
  background-color: rgba(200, 200, 200, 0.2);
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 0.8; }
  100% { opacity: 0.6; }
}
