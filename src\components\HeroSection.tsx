import React, { useEffect, useRef, useState, useMemo } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import ScrollReveal from './ScrollReveal';
import Logo from './Logo';
import OrbitingIcons from './OrbitingIcons';
import { Code, Camera, ChevronDown } from 'lucide-react'; // Added ChevronDown for scroll indicator

const HeroSection: React.FC = () => {
  const { t } = useLanguage();
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);
  
  // Handle scroll action when indicator is clicked
  const handleScrollClick = () => {
    const nextSection = document.querySelector('#home + section');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Cleanup function for animations
  useEffect(() => {
    return () => {
      // Cleanup any animations if needed
    };
  }, []);

  return (
    <section id="home" className="min-h-[100vh] flex flex-col items-center justify-between py-8 sm:py-16 md:py-20 px-3 sm:px-6 md:px-8 relative overflow-hidden">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Animated Orbital Icons System */}
      <OrbitingIcons className="z-0" />

      {/* Enhanced Decorative Code Icon (Top Right) - Hero Context - Static - Better mobile sizing */}
      <div className="absolute top-0 right-0 w-20 h-20 sm:w-32 sm:h-32 md:w-48 md:h-48 text-neon-cyan/15 transform translate-x-1/6 -translate-y-1/6 sm:translate-x-1/4 sm:-translate-y-1/4 md:translate-x-1/3 md:-translate-y-1/3 rotate-12 opacity-40 z-10">
        <Code className="w-full h-full" />
      </div>

      {/* Enhanced Decorative Camera Icon (Bottom Left) - Hero Context - Static - Better mobile sizing */}
      <div className="absolute bottom-0 left-0 w-20 h-20 sm:w-32 sm:h-32 md:w-48 md:h-48 text-neon-purple/15 transform -translate-x-1/6 translate-y-1/6 sm:-translate-x-1/4 sm:translate-y-1/4 md:-translate-x-1/3 md:translate-y-1/3 -rotate-12 opacity-40 z-10">
        <Camera className="w-full h-full" />
      </div>

      {/* Simplified gradient orbs for cleaner appearance */}
      <div className="absolute top-20 right-1/4 w-32 h-32 sm:w-40 sm:h-40 md:w-64 md:h-64 bg-gradient-ocean opacity-15 rounded-full blur-3xl z-5"></div>
      <div className="absolute bottom-20 left-1/4 w-36 h-36 sm:w-48 sm:h-48 md:w-80 md:h-80 bg-gradient-sunset opacity-10 rounded-full blur-3xl z-5"></div>

      <div className="container max-w-5xl mx-auto text-center relative z-20 flex-1 flex flex-col items-center justify-center">
        {/* Centered content with enhanced z-index */}
        <div className="flex flex-col items-center animate-fade-in w-full">
          <ScrollReveal delay={300} className="animate-float w-[85%] sm:w-auto max-w-full">
            <div className="inline-block mb-5 sm:mb-6 px-5 sm:px-6 md:px-8 lg:px-10 py-3 sm:py-3 md:py-4 lg:py-5 rounded-full premium-glass transition-all duration-300">
              <Logo variant="hero" className="transform scale-110 sm:scale-100" />
            </div>
          </ScrollReveal>

          <ScrollReveal delay={500}>
            <h1 className="text-3xl sm:text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-5 md:mb-8 leading-tight max-w-4xl px-3 sm:px-0">
              <TypedText className="text-gradient-aurora drop-shadow-glow" />
            </h1>
          </ScrollReveal>

          <ScrollReveal delay={700}>
            <p className="text-base sm:text-base md:text-xl lg:text-2xl text-foreground/80 mb-6 sm:mb-6 md:mb-12 max-w-3xl mx-auto px-4 sm:px-2 leading-relaxed animate-fade-up">
              Premium web development and digital experiences for luxury brands
            </p>
          </ScrollReveal>


        </div>
      </div>

      {/* Scroll Down Animation repositioned to bottom of hero section */}
      <div className="w-full flex justify-center mb-6 sm:mb-8 md:mb-10 relative z-20">
        <ScrollReveal delay={900}>
          <div 
            ref={scrollIndicatorRef} 
            onClick={handleScrollClick} 
            className="cursor-pointer transition-all duration-300 animate-pulse-subtle hover:scale-110 active:scale-95 py-2 px-4 min-h-[48px] min-w-[48px] touch-manipulation"
          >
            <div className="flex flex-col items-center gap-2 sm:gap-2">
              <span className="text-premium-gold/80 text-sm sm:text-sm tracking-widest uppercase">{t('scroll.down') || 'Scroll down'}</span>
              <div className="relative py-1">
                <div className="absolute animate-chevron-1">
                  <ChevronDown size={24} className="sm:size-6 text-premium-gold/80" />
                </div>
                <div className="absolute animate-chevron-2">
                  <ChevronDown size={24} className="sm:size-6 text-premium-gold/60" />
                </div>
                <div className="animate-chevron-3">
                  <ChevronDown size={24} className="sm:size-6 text-premium-gold/40" />
                </div>
              </div>
            </div>
          </div>
        </ScrollReveal>
      </div>
    </section>
  );
};

// TypedText component for animated typing effect
interface TypedTextProps {
  className?: string;
}

const TypedText: React.FC<TypedTextProps> = ({ className }) => {
  // Array of phrases to cycle through - wrapped in useMemo to prevent re-renders
  const phrases = useMemo(() => [
    "We Build Digital Experiences",
    "Creating Modern Web Solutions",
    "Transforming Ideas Into Reality",
    "Expert Development & Design",
    "Your Digital Growth Partner",
    "Innovative Technology Solutions",
    "Crafting Premium Websites",
    "Elevating Your Online Presence"
  ], []);

  // Simple state for the animation
  const [displayText, setDisplayText] = useState('');
  const [phraseIndex, setPhraseIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [typingSpeed, setTypingSpeed] = useState(60); // milliseconds per character
  
  // Cursor blinking
  const [showCursor, setShowCursor] = useState(true);
  
  // Check for reduced motion preference
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  
  // Start cursor blinking effect
  useEffect(() => {
    // Client-side only code
    if (typeof window !== 'undefined') {
      // Check for reduced motion preference
      setPrefersReducedMotion(window.matchMedia?.('(prefers-reduced-motion: reduce)').matches);
      
      // Set up cursor blinking
      const cursorInterval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);
      
      return () => clearInterval(cursorInterval);
    }
    return () => {};
  }, []);
  
  // Handle the typing animation
  useEffect(() => {
    // For users who prefer reduced motion, just display full text
    if (prefersReducedMotion) {
      setDisplayText(phrases[0]);
      return;
    }
    
    // Get the current phrase
    const currentPhrase = phrases[phraseIndex];
    
    // Set up a timeout for the next typing/deleting step
    let timeout: NodeJS.Timeout;
    
    if (isDeleting) {
      // If we're deleting
      timeout = setTimeout(() => {
        setDisplayText(prevText => prevText.substring(0, prevText.length - 1));
        
        // Speed up deletion slightly
        setTypingSpeed(40);
        
        // If we've deleted everything, move to the next phrase
        if (displayText.length <= 1) {
          setIsDeleting(false);
          setPhraseIndex((prev) => (prev + 1) % phrases.length);
          setTypingSpeed(60);
        }
      }, typingSpeed);
    } else {
      // If we're typing
      if (displayText === currentPhrase) {
        // If we've typed the whole phrase, pause before deleting
        timeout = setTimeout(() => {
          setIsDeleting(true);
        }, 3000); // 3 second pause when completed typing
      } else {
        // Continue typing the current phrase
        timeout = setTimeout(() => {
          setDisplayText(currentPhrase.substring(0, displayText.length + 1));
        }, typingSpeed);
      }
    }
    
    // Cleanup function
    return () => clearTimeout(timeout);
  }, [displayText, phraseIndex, isDeleting, typingSpeed, phrases, prefersReducedMotion]);
  
  // Log state for debugging
  useEffect(() => {
    console.log('Typing state:', {
      displayText,
      phraseIndex,
      currentPhrase: phrases[phraseIndex],
      isDeleting,
      typingSpeed
    });
  }, [displayText, phraseIndex, isDeleting, typingSpeed, phrases]);

  return (
    <span className={className} data-testid="typed-text">
      <span data-phrase-index={phraseIndex} data-is-deleting={isDeleting}>{displayText}</span>
      <span 
        className={`inline-block ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity`}
        aria-hidden="true"
      >|</span>
      {/* Fallback for screen readers and reduced motion users */}
      <span className="sr-only">Econic Media - Digital Services Agency</span>
    </span>
  );
};

export default HeroSection;

/* Add these styles to your global CSS or create a new stylesheet */
/**
 * To add these animations, include the following CSS in your global.css or create a new file:
 *
 * @keyframes fadeIn {
 *   from { opacity: 0; }
 *   to { opacity: 1; }
 * }
 * 
 * @keyframes fadeUp {
 *   from { opacity: 0; transform: translateY(20px); }
 *   to { opacity: 1; transform: translateY(0); }
 * }
 * 
 * @keyframes textReveal {
 *   from { clip-path: inset(0 100% 0 0); }
 *   to { clip-path: inset(0 0 0 0); }
 * }
 * 
 * @keyframes pulseSubtle {
 *   0%, 100% { opacity: 1; transform: scale(1); }
 *   50% { opacity: 0.8; transform: scale(0.98); }
 * }
 * 
 * @keyframes chevronBounce1 {
 *   0%, 100% { transform: translateY(0); }
 *   50% { transform: translateY(8px); }
 * }
 * 
 * @keyframes chevronBounce2 {
 *   0%, 100% { transform: translateY(8px); }
 *   50% { transform: translateY(16px); }
 * }
 * 
 * @keyframes chevronBounce3 {
 *   0%, 100% { transform: translateY(16px); }
 *   50% { transform: translateY(24px); }
 * }
 *
 * .animate-fade-in {
 *   animation: fadeIn 0.8s ease-out forwards;
 * }
 * 
 * .animate-fade-up {
 *   animation: fadeUp 0.8s ease-out forwards;
 * }
 * 
 * .animate-text-reveal {
 *   animation: textReveal 1s cubic-bezier(0.77, 0, 0.175, 1) forwards;
 *   display: inline-block;
 * }
 * 
 * .animate-pulse-subtle {
 *   animation: pulseSubtle 2s ease-in-out infinite;
 * }
 * 
 * .animate-chevron-1 {
 *   animation: chevronBounce1 1.5s ease-in-out infinite;
 * }
 * 
 * .animate-chevron-2 {
 *   animation: chevronBounce2 1.5s ease-in-out infinite;
 * }
 * 
 * .animate-chevron-3 {
 *   animation: chevronBounce3 1.5s ease-in-out infinite;
 * }
 *
 * @media (prefers-reduced-motion: reduce) {
 *   .animate-fade-in,
 *   .animate-fade-up,
 *   .animate-text-reveal,
 *   .animate-pulse-subtle,
 *   .animate-chevron-1,
 *   .animate-chevron-2,
 *   .animate-chevron-3 {
 *     animation: none;
 *   }
 * }
 */
